<!DOCTYPE html>
<html>
<head>
  <title>Study Intent</title>
  <style>
    body { font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
    .container { text-align: center; }
    input { padding: 10px; width: 300px; margin: 10px; }
    button { padding: 10px 20px; cursor: pointer; }
  </style>
</head>
<body>
  <div class="container">
    <h2>Enter Your Study Intent</h2>
    <input type="text" id="intent" placeholder="e.g., Researching calculus" />
    <button onclick="submitIntent()">Submit</button>
    <script>
      function submitIntent() {
        const intent = document.getElementById('intent').value;
        if (intent) {
          fetch('http://localhost:3000/submit-intent', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ intent })
          }).then(() => window.close());
        }
      }
    </script>
  </div>
</body>
</html>