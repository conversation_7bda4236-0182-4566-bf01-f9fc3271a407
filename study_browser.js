const { chromium } = require('playwright');
const fs = require('fs').promises;
const path = require('path');
const { Groq } = require('groq-sdk');
const net = require('net');
require('dotenv').config();

// File paths
const logFilePath = path.join(__dirname, 'data/activity_log.json');
const sitesFilePath = path.join(__dirname, 'data/sites.json');
const intentHtmlPath = path.join(__dirname, 'assets/intent.html');

// Initialize data structures
let activityLog = [];
let sitesData = { safe: [], blacklist: [] };
let activeTabStartTime = {};
let previousSafeUrl = {};
let userIntent = '';
let popupSocket = null;

// Load environment variables
const GROQ_API_KEY = process.env.GROQ_API_KEY;
if (!GROQ_API_KEY) {
  console.error('GROQ_API_KEY not set in .env file');
  process.exit(1);
}

// Initialize Groq client
const groq = new Groq({
  apiKey: GROQ_API_KEY
});

// Connect to Electron TCP server
async function connectToPopupServer() {
  return new Promise((resolve, reject) => {
    popupSocket = net.createConnection({ host: 'localhost', port: 3001 }, () => {
      console.log('Connected to Electron popup server');
      resolve();
    });
    popupSocket.on('error', (error) => {
      console.error('Popup server connection error:', error);
      reject(error);
    });
  });
}

// Send popup message
async function showPopup(message, type) {
  if (popupSocket && !popupSocket.destroyed) {
    popupSocket.write(JSON.stringify({ message, type }) + '\n');
    console.log(`Sent popup message: ${message} (${type})`);
  } else {
    console.log('Popup server not connected, skipping popup');
  }
}

// HTML for intent prompt
const intentPromptHtml = `
<!DOCTYPE html>
<html>
<head>
  <title>Study Intent</title>
  <style>
    body { font-family: Arial, sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; margin: 0; }
    .container { text-align: center; }
    input { padding: 10px; width: 300px; margin: 10px; }
    button { padding: 10px 20px; cursor: pointer; }
  </style>
</head>
<body>
  <div class="container">
    <h2>Enter Your Study Intent</h2>
    <input type="text" id="intent" placeholder="e.g., Researching calculus" />
    <button onclick="submitIntent()">Submit</button>
    <script>
      function submitIntent() {
        const intent = document.getElementById('intent').value;
        if (intent) {
          localStorage.setItem('studyIntent', intent);
          window.location.href = 'about:blank';
        }
      }
    </script>
  </div>
</body>
</html>
`;

// Load existing logs and sites
async function loadExistingData() {
  try {
    const logData = await fs.readFile(logFilePath, 'utf8');
    activityLog = JSON.parse(logData);
  } catch (error) {
    console.log('No existing activity log found, starting fresh.');
    activityLog = [];
  }
  try {
    const sitesDataRaw = await fs.readFile(sitesFilePath, 'utf8');
    sitesData = JSON.parse(sitesDataRaw);
    
    // Clean up: Remove any search platforms from blacklist if they exist
    const searchPlatformDomains = [
      'google.com', 'www.google.com', 'youtube.com', 'www.youtube.com',
      'bing.com', 'www.bing.com', 'search.yahoo.com', 'duckduckgo.com',
      'yandex.com', 'baidu.com'
    ];
    
    let removedPlatforms = [];
    sitesData.blacklist = sitesData.blacklist.filter(domain => {
      const shouldRemove = searchPlatformDomains.some(platform => 
        domain.includes(platform) || platform.includes(domain)
      );
      if (shouldRemove) {
        removedPlatforms.push(domain);
      }
      return !shouldRemove;
    });
    
    // Ensure search platforms are in safe list
    searchPlatformDomains.forEach(domain => {
      if (!sitesData.safe.includes(domain)) {
        sitesData.safe.push(domain);
      }
    });
    
    if (removedPlatforms.length > 0) {
      console.log('Removed search platforms from blacklist:', removedPlatforms);
      await saveSites();
    }
    
  } catch (error) {
    console.log('No existing sites data found, starting fresh.');
    // Initialize with search platforms in safe list
    sitesData = { 
      safe: [
        'google.com', 'www.google.com', 'youtube.com', 'www.youtube.com',
        'bing.com', 'www.bing.com', 'search.yahoo.com', 'duckduckgo.com'
      ], 
      blacklist: [] 
    };
  }
}

// Save data to files
async function saveLog() {
  try {
    await fs.writeFile(logFilePath, JSON.stringify(activityLog, null, 2));
    console.log('Activity log saved.');
  } catch (error) {
    console.error('Error saving activity log:', error);
  }
}

async function saveSites() {
  try {
    await fs.writeFile(sitesFilePath, JSON.stringify(sitesData, null, 2));
    console.log('Sites data saved.');
  } catch (error) {
    console.error('Error saving sites data:', error);
  }
}

// Generate tab ID
function generateTabId(page) {
  return page.url() + '-' + Date.now();
}

// Extract domain from URL
function getDomain(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return '';
  }
}

// Prompt for study intent using local HTML file
async function promptStudyIntent(page) {
  return new Promise(async (resolve, reject) => {
    try {
      // Write HTML to file
      await fs.writeFile(intentHtmlPath, intentPromptHtml);
      console.log('Intent prompt HTML written to', intentHtmlPath);

      // Navigate to file
      await page.goto(`file://${intentHtmlPath}`, { waitUntil: 'load' });
      console.log('Navigated to intent prompt page');

      // Wait for navigation to about:blank after submission
      await page.waitForURL('about:blank', { timeout: 60000 });
      console.log('Detected navigation to about:blank');

      // Retrieve intent from localStorage
      userIntent = await page.evaluate(() => localStorage.getItem('studyIntent') || 'General studying');
      console.log('Retrieved study intent:', userIntent);
      resolve();
    } catch (error) {
      console.error('Failed to capture study intent:', error);
      reject(error);
    }
  });
}

// Check if a domain should never be blocked (Google, YouTube, Bing, etc.)
function isSearchPlatform(domain) {
  const searchPlatforms = [
    'google.com', 'youtube.com', 'bing.com', 'yahoo.com', 'duckduckgo.com',
    'search.yahoo.com', 'yandex.com', 'baidu.com'
  ];
  return searchPlatforms.some(platform => 
    domain.includes(platform) || platform.includes(domain)
  );
}

// Check if URL is a direct video/content page (not a search)
function isContentPage(url) {
  // YouTube video pages
  if (url.includes('youtube.com/watch')) return true;
  
  // Google services (Gmail, Drive, etc.)
  if (url.includes('mail.google.com') || url.includes('drive.google.com') || 
      url.includes('docs.google.com') || url.includes('sheets.google.com')) return true;
      
  return false;
}

// Extract search query from URL
function extractSearchQuery(url) {
  try {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;
    
    // Google search
    if (url.includes('google.com/search')) {
      return params.get('q') || '';
    }
    
    // YouTube search
    if (url.includes('youtube.com/results')) {
      return params.get('search_query') || '';
    }
    
    // Bing search
    if (url.includes('bing.com/search')) {
      return params.get('q') || '';
    }
    
    // Yahoo search
    if (url.includes('search.yahoo.com/search')) {
      return params.get('p') || '';
    }
    
    return '';
  } catch {
    return '';
  }
}

// Evaluate search query relevance using Groq
async function evaluateSearchQuery(query, intent) {
  if (!query.trim()) return true; // Allow empty queries
  
  try {
    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: `You are an AI assistant that evaluates whether search queries are relevant to a user's learning domain.

TASK: Analyze if the search query is relevant to the user's learning domain and respond with a JSON object.

EVALUATION CRITERIA:
1. Direct Relevance: Is the query directly related to the learning domain?
2. Educational Intent: Does the query suggest educational/learning purposes?
3. Topic Alignment: Does the query align with studying the specified domain?

RELEVANT EXAMPLES:
- Learning Domain: "Machine Learning" → Query: "neural networks tutorial" ✓
- Learning Domain: "Web Development" → Query: "JavaScript functions" ✓
- Learning Domain: "Physics" → Query: "quantum mechanics explained" ✓

IRRELEVANT EXAMPLES:
- Learning Domain: "Machine Learning" → Query: "funny cat videos" ✗
- Learning Domain: "Web Development" → Query: "latest celebrity news" ✗
- Learning Domain: "Physics" → Query: "Instagram stories" ✗

SPECIAL CONSIDERATIONS:
- Be lenient with broad educational queries that might tangentially relate
- Consider that users might search for tools, software, or resources related to their domain
- Allow queries for study breaks if they're clearly short-term (e.g., "study music")
- Block clearly entertainment/social media focused queries

Respond ONLY with a JSON object in this exact format:
{"isRelevant": true/false, "reasoning": "brief explanation", "confidence": 0.0-1.0}`
        },
        {
          role: "user",
          content: `USER'S LEARNING DOMAIN: "${intent}"
SEARCH QUERY: "${query}"`
        }
      ],
      model: "openai/gpt-oss-20b",
      temperature: 0.1,
      max_completion_tokens: 200,
      top_p: 0.8
    });

    const responseContent = chatCompletion.choices[0]?.message?.content;
    if (!responseContent) {
      console.error('No response content from Groq for query evaluation');
      return true; // Default to allowing if API fails
    }

    // Parse the JSON response
    const evaluation = JSON.parse(responseContent);
    
    console.log('Groq query evaluation:', {
      isRelevant: evaluation.isRelevant,
      reasoning: evaluation.reasoning,
      confidence: evaluation.confidence,
      query: query
    });

    return evaluation.isRelevant;
  } catch (error) {
    console.error('Groq API error for query evaluation:', error.message);
    // Fallback: allow the query if API fails
    return true;
  }
}

// Groq API call for page content evaluation
async function evaluateWithGroq(url, title, intent) {
  try {
    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: `You are an AI assistant that evaluates whether websites are appropriate for studying and educational purposes.

TASK: Analyze the provided webpage information and determine if it aligns with the user's study intent. Respond with a JSON object.

EVALUATION CRITERIA:
1. Educational Relevance: Does the content directly relate to the study intent?
2. Academic Quality: Is this from a reputable educational source (universities, educational institutions, academic publishers, established learning platforms)?
3. Content Type: Is it educational material (tutorials, courses, research papers, documentation, reference materials)?
4. Distraction Level: Is the site likely to cause distraction from studying (social media, entertainment, gaming, news, shopping)?

SAFE CATEGORIES (mark as safe):
- Educational websites (.edu domains, Khan Academy, Coursera, edX, etc.)
- Academic research and journals
- Documentation and technical references
- Libraries and educational databases
- Study tools and calculators
- Language learning platforms
- Programming tutorials and coding resources
- Scientific and technical resources
- Educational YouTube videos and channels
- Wikipedia articles related to the study topic

UNSAFE CATEGORIES (mark as unsafe):
- Social media platforms (Facebook, Twitter, Instagram, TikTok, etc.)
- Entertainment content (non-educational YouTube, Netflix, gaming sites)
- News and current events (unless directly related to study topic)
- Shopping and e-commerce sites
- Forums and discussion boards (unless academic/educational)
- Personal blogs (unless from recognized experts)
- Adult content or inappropriate material

SPECIAL CONSIDERATIONS:
- If the URL domain suggests educational content but title is unclear, lean towards safe
- If the study intent is broad (like "general studying"), be more lenient with educational resources
- If the study intent is specific, ensure strong relevance to the topic
- Consider context: a YouTube video about calculus for a "mathematics" intent should be safe
- IMPORTANT: Google and YouTube search pages themselves should always be allowed - only evaluate result pages

Respond ONLY with a JSON object in this exact format:
{"isSafe": true/false, "reasoning": "brief explanation", "confidence": 0.0-1.0}`
        },
        {
          role: "user",
          content: `USER'S STUDY INTENT: "${intent}"

WEBPAGE INFORMATION:
- URL: ${url}
- Title: ${title}`
        }
      ],
      model: "openai/gpt-oss-20b",
      temperature: 0.1,
      max_completion_tokens: 200,
      top_p: 0.8
    });

    const responseContent = chatCompletion.choices[0]?.message?.content;
    if (!responseContent) {
      console.error('No response content from Groq for page evaluation');
      return false;
    }

    // Parse the JSON response
    const evaluation = JSON.parse(responseContent);
    
    console.log('Groq evaluation:', {
      isSafe: evaluation.isSafe,
      reasoning: evaluation.reasoning,
      confidence: evaluation.confidence,
      url: url
    });

    return evaluation.isSafe;
  } catch (error) {
    console.error('Groq API error:', {
      message: error.message,
      url: url
    });

    // More intelligent fallback based on URL patterns
    const domain = getDomain(url).toLowerCase();
    const educationalDomains = [
      '.edu', 'khan', 'coursera', 'edx', 'udemy', 'stackoverflow',
      'github', 'wikipedia', 'scholar.google', 'arxiv', 'jstor',
      'pubmed', 'researchgate', 'academia.edu', 'mit.edu', 'stanford.edu',
      'geeksforgeeks'
    ];

    const isLikelyEducational = educationalDomains.some(pattern =>
      domain.includes(pattern)
    );

    console.log(`Fallback evaluation for ${domain}: ${isLikelyEducational ? 'safe' : 'unsafe'}`);
    return isLikelyEducational;
  }
}

// Main function
(async () => {
  // Load existing data
  await loadExistingData();

  // Connect to Electron popup server
  try {
    await connectToPopupServer();
  } catch (error) {
    console.error('Failed to connect to popup server, continuing without popups');
  }

  // Launch browser
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  let page = await context.newPage();

  // Prompt for study intent
  try {
    console.log('Prompting for study intent');
    await promptStudyIntent(page);
    console.log('Received study intent:', userIntent);
    await page.close();
  } catch (error) {
    console.error('Failed to capture study intent:', error);
    await browser.close();
    process.exit(1);
  } finally {
    // Clean up HTML file
    try {
      await fs.unlink(intentHtmlPath);
      console.log('Cleaned up intent prompt HTML file');
    } catch (error) {
      console.log('No intent prompt HTML file to clean up');
    }
  }

  // Open new tab for browsing
  page = await context.newPage();
  await page.goto('about:blank');

  // Track time and activity
  async function trackPageTime(page, tabId) {
    activeTabStartTime[tabId] = Date.now();
    page.on('close', async () => {
      const endTime = Date.now();
      const timeSpent = (endTime - activeTabStartTime[tabId]) / 1000;
      activityLog.push({
        tabId,
        url: page.url(),
        event: 'time_spent',
        timeSpentSeconds: timeSpent,
        timestamp: new Date().toISOString()
      });
      await saveLog();
      delete activeTabStartTime[tabId];
      delete previousSafeUrl[tabId];
    });
  }

  // Check URL against safe/blacklist with enhanced search filtering
  async function checkAndEvaluateUrl(page, tabId, url) {
    const domain = getDomain(url);
    if (!domain) {
      console.log('Invalid domain for URL:', url);
      return;
    }

    // NEVER block search platforms themselves
    if (isSearchPlatform(domain)) {
      console.log(`Search platform ${domain} detected - always allowed`);
      
      // Check if this is a search query URL
      const searchQuery = extractSearchQuery(url);
      if (searchQuery) {
        console.log(`Search query detected: "${searchQuery}"`);
        await showPopup(`Evaluating search query: "${searchQuery}"...`, 'info');
        
        const isQueryRelevant = await evaluateSearchQuery(searchQuery, userIntent);
        if (!isQueryRelevant) {
          console.log(`Irrelevant search query blocked: "${searchQuery}"`);
          activityLog.push({
            tabId,
            url,
            searchQuery,
            event: 'search_query_blocked',
            timestamp: new Date().toISOString()
          });
          await saveLog();
          await showPopup(`Search query "${searchQuery}" is not relevant to your learning domain "${userIntent}"`, 'error');
          const prevUrl = previousSafeUrl[tabId] || 'https://www.google.com';
          await page.goto(prevUrl);
          return;
        } else {
          console.log(`Search query allowed: "${searchQuery}"`);
          activityLog.push({
            tabId,
            url,
            searchQuery,
            event: 'search_query_allowed',
            timestamp: new Date().toISOString()
          });
          await saveLog();
          await showPopup(`Search query "${searchQuery}" is relevant to your learning domain`, 'success');
        }
      } else if (isContentPage(url)) {
        // This is a content page (e.g., YouTube video) - evaluate it like any other page
        console.log(`Content page on search platform detected: ${url}`);
        // Continue to evaluation below
      } else {
        // Home pages, navigation pages - always allow
        console.log(`Navigation page on search platform - always allowed`);
      }
      
      // Always store search platforms as safe
      previousSafeUrl[tabId] = url;
      
      // If it's not a content page that needs evaluation, return here
      if (!isContentPage(url)) {
        return;
      }
    }

    // Check regular blacklist
    if (sitesData.blacklist.includes(domain)) {
      activityLog.push({
        tabId,
        url,
        event: 'blacklisted',
        timestamp: new Date().toISOString()
      });
      await saveLog();
      const prevUrl = previousSafeUrl[tabId] || 'https://www.google.com';
      console.log(`Blacklisted domain ${domain}, redirecting to ${prevUrl}`);
      await showPopup(`Site ${domain} is blacklisted!`, 'error');
      await page.goto(prevUrl);
      return;
    }

    // Check if already in safe list
    if (sitesData.safe.includes(domain)) {
      previousSafeUrl[tabId] = url;
      activityLog.push({
        tabId,
        url,
        event: 'safe',
        timestamp: new Date().toISOString()
      });
      await saveLog();
      console.log(`Safe domain ${domain}, continuing`);
      await showPopup(`Site ${domain} is safe for studying.`, 'success');
      return;
    }

    // Wait and evaluate new sites (result/page filtering)
    console.log(`Waiting 10 seconds to evaluate ${url}`);
    await showPopup(`Evaluating ${domain} for study relevance...`, 'info');
    await new Promise(resolve => setTimeout(resolve, 10000));
    if (page.isClosed() || page.url() !== url) {
      console.log(`Page closed or URL changed, skipping evaluation for ${url}`);
      return;
    }

    const title = await page.title();
    console.log(`Evaluating page content: ${url} with title: ${title}`);
    const isSafe = await evaluateWithGroq(url, title, userIntent);

    if (isSafe) {
      sitesData.safe.push(domain);
      previousSafeUrl[tabId] = url;
      activityLog.push({
        tabId,
        url,
        event: 'safe',
        timestamp: new Date().toISOString()
      });
      console.log(`Marked ${domain} as safe`);
      await showPopup(`Site ${domain} is safe for studying.`, 'success');
    } else {
      sitesData.blacklist.push(domain);
      activityLog.push({
        tabId,
        url,
        event: 'blacklisted',
        timestamp: new Date().toISOString()
      });
      const prevUrl = previousSafeUrl[tabId] || 'https://www.google.com';
      console.log(`Marked ${domain} as blacklisted, redirecting to ${prevUrl}`);
      await showPopup(`Site ${domain} is not study-related and has been blacklisted.`, 'error');
      await page.goto(prevUrl);
    }
    await saveSites();
    await saveLog();
  }

  // Track navigation
  page.on('load', async () => {
    const tabId = generateTabId(page);
    const url = page.url();
    if (url.startsWith('http')) {
      console.log(`Navigated to ${url}`);
      await trackPageTime(page, tabId);
      await checkAndEvaluateUrl(page, tabId, url);
    }
  });

  // Handle new tabs
  context.on('page', async (newPage) => {
    page = newPage;
    page.on('load', async () => {
      const tabId = generateTabId(page);
      const url = page.url();
      if (url.startsWith('http')) {
        console.log(`New tab navigated to ${url}`);
        await trackPageTime(page, tabId);
        await checkAndEvaluateUrl(page, tabId, url);
      }
    });
  });

  // Keep running until browser closes
  await new Promise((resolve) => {
    browser.on('close', resolve);
  });

  // Clean up
  if (popupSocket) {
    popupSocket.end();
  }
  await saveLog();
  await saveSites();
  await browser.close();
})();