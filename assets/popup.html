<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html>
<head>
  <title>Study Popup</title>
  <style>
    body {
      margin: 0;
      font-family: Arial, sans-serif;
      background: transparent;
    }
    .popup {
      background-color: #333;
      color: white;
      padding: 10px 20px;
      border-radius: 5px;
      opacity: 0;
      transition: opacity 0.5s ease-in-out;
      max-width: 300px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.2);
      text-align: center;
    }
    .popup.show {
      opacity: 1;
    }
    .popup.error {
      background-color: #d32f2f;
    }
    .popup.success {
      background-color: #4caf50;
    }
  </style>
</head>
<body>
  <div id="popup" class="popup"></div>
  <script>
    const { ipcRenderer } = require('electron');

    ipcRenderer.on('show-popup', (event, { message, type }) => {
      const popup = document.getElementById('popup');
      popup.textContent = message;
      popup.className = `popup ${type}`;
      popup.classList.add('show');
      setTimeout(() => {
        popup.classList.remove('show');
      }, 3000);
    });
  </script>
</body>
</html>