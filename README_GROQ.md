# Study Browser - Groq Integration

## Overview
The Study Browser now uses Groq's AI models instead of Gemini for evaluating search queries and web pages. This provides fast, reliable AI-powered filtering for maintaining study focus.

## ⚡ Groq AI Features

### Model Used: `openai/gpt-oss-20b`
- **High Performance**: Fast response times for real-time filtering
- **Reliable**: Consistent evaluation of search queries and content
- **Cost-Effective**: Efficient API usage with JSON-based responses

## 🔧 Setup Instructions

### 1. Get Groq API Key
1. Visit [Groq Console](https://console.groq.com/)
2. Sign up/Login to your account
3. Navigate to API Keys section
4. Create a new API key

### 2. Configure Environment
Add your Groq API key to the `.env` file:
```properties
GROQ_API_KEY=your_actual_groq_api_key_here
```

### 3. Install Dependencies
```bash
npm install
```

### 4. Test Connection
```bash
node test_groq_connection.js
```

## 🔍 How It Works

### Search Query Filtering
```javascript
// Example: Machine Learning domain
Query: "neural networks tutorial" → ✅ ALLOWED (relevant)
Query: "funny cat videos" → ❌ BLOCKED (irrelevant)
```

### Page Content Evaluation
```javascript
// Educational content → ALLOWED
URL: "https://www.khanacademy.org/ml-course"
Title: "Machine Learning Fundamentals"
Result: ✅ SAFE

// Entertainment content → BLOCKED  
URL: "https://www.instagram.com"
Title: "Instagram"
Result: ❌ UNSAFE
```

## 📊 API Response Format

### Query Evaluation Response:
```json
{
  "isRelevant": true,
  "reasoning": "Neural networks are a core ML concept",
  "confidence": 0.95
}
```

### Page Evaluation Response:
```json
{
  "isSafe": true,
  "reasoning": "Educational content from reputable source",
  "confidence": 0.9
}
```

## 🚀 Performance Benefits

### Groq vs Gemini:
- ⚡ **Faster Response Times**: Groq provides quicker API responses
- 💰 **Cost Effective**: Better pricing for high-volume usage
- 🔒 **Reliable**: More consistent availability and performance
- 🎯 **Focused**: Direct JSON responses without function calling overhead

## 🧪 Testing

### Test Groq Connection:
```bash
node test_groq_connection.js
```

### Test Search Query Filtering:
```bash
node test_search_filtering.js
```

### Test URL Parsing:
```bash
node test_url_parsing.js
```

## 🔄 Migration from Gemini

### Changes Made:
1. **Dependencies**: Replaced `axios` with `groq-sdk`
2. **API Calls**: Simplified from function-calling to direct JSON responses
3. **Environment**: Added `GROQ_API_KEY` to `.env`
4. **Response Parsing**: Direct JSON parsing instead of function call extraction

### Compatibility:
- ✅ All existing functionality preserved
- ✅ Same filtering logic and criteria
- ✅ Improved response times and reliability
- ✅ Same user experience and interface

## 📝 Usage Examples

### Starting the Browser:
```bash
npm start
```

### Example Workflow:
1. **Enter Learning Domain**: "Web Development"
2. **Search Google**: "JavaScript async functions" → ✅ ALLOWED
3. **Click Result**: MDN Documentation → ✅ ALLOWED
4. **Try Entertainment**: "TikTok videos" → ❌ BLOCKED

## 🛠️ Troubleshooting

### Common Issues:

#### "GROQ_API_KEY not set"
- Ensure your `.env` file contains the correct API key
- Check for typos in the variable name

#### "API Error" 
- Verify your API key is valid and active
- Check your Groq account balance/credits
- Ensure stable internet connection

#### "JSON Parse Error"
- Usually indicates API response format issues
- System will fall back to safe defaults

## 📈 Monitoring

### Activity Logging:
All evaluations are logged in `activity_log.json`:
```json
{
  "tabId": "...",
  "url": "...",
  "searchQuery": "neural networks",
  "event": "search_query_allowed",
  "timestamp": "2025-01-16T..."
}
```

## 🔮 Future Enhancements

- **Streaming Responses**: Implement real-time evaluation feedback
- **Custom Models**: Option to use different Groq models
- **Batch Processing**: Evaluate multiple queries simultaneously
- **Caching**: Store evaluation results for repeated queries
- **Analytics**: Enhanced reporting on filtering decisions

---

**✅ Ready to use!** Your Study Browser now runs on Groq's powerful AI infrastructure for better performance and reliability.
