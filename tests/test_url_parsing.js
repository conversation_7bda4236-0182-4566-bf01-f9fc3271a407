// Test the URL parsing and search query extraction functionality
const { URL } = require('url');

// Extract search query from URL
function extractSearchQuery(url) {
  try {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;
    
    // Google search
    if (url.includes('google.com/search')) {
      return params.get('q') || '';
    }
    
    // YouTube search
    if (url.includes('youtube.com/results')) {
      return params.get('search_query') || '';
    }
    
    // Bing search
    if (url.includes('bing.com/search')) {
      return params.get('q') || '';
    }
    
    // Yahoo search
    if (url.includes('search.yahoo.com/search')) {
      return params.get('p') || '';
    }
    
    return '';
  } catch {
    return '';
  }
}

// Check if a domain should never be blocked (Google, YouTube, Bing, etc.)
function isSearchPlatform(domain) {
  const searchPlatforms = [
    'google.com', 'youtube.com', 'bing.com', 'yahoo.com', 'duckduckgo.com',
    'search.yahoo.com', 'yandex.com', 'baidu.com'
  ];
  return searchPlatforms.some(platform => domain.includes(platform));
}

// Extract domain from URL
function getDomain(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return '';
  }
}

// Test cases
const testUrls = [
  'https://www.google.com/search?q=machine+learning+tutorial',
  'https://www.youtube.com/results?search_query=python+programming',
  'https://www.bing.com/search?q=web+development',
  'https://search.yahoo.com/search?p=artificial+intelligence',
  'https://www.google.com/',
  'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
  'https://stackoverflow.com/questions/tagged/javascript',
  'https://www.facebook.com',
  'https://en.wikipedia.org/wiki/Machine_learning'
];

console.log('🔍 Testing URL Analysis and Search Query Extraction\n');

testUrls.forEach((url, index) => {
  console.log(`${index + 1}. URL: ${url}`);
  
  const domain = getDomain(url);
  console.log(`   Domain: ${domain}`);
  
  const isSearchPlatformDomain = isSearchPlatform(domain);
  console.log(`   Is Search Platform: ${isSearchPlatformDomain ? '✅ Yes' : '❌ No'}`);
  
  const searchQuery = extractSearchQuery(url);
  if (searchQuery) {
    console.log(`   Search Query: "${searchQuery}"`);
    console.log(`   Action: ${isSearchPlatformDomain ? 'Evaluate query relevance' : 'N/A'}`);
  } else if (isSearchPlatformDomain) {
    console.log(`   Search Query: None (home page or video page)`);
    console.log(`   Action: Always allow`);
  } else {
    console.log(`   Search Query: N/A`);
    console.log(`   Action: Evaluate page content`);
  }
  
  console.log('');
});

console.log('📋 Summary of New Behavior:');
console.log('✅ Search platforms (Google, YouTube, etc.) are never blocked');
console.log('🔍 Search queries on these platforms are evaluated for relevance');
console.log('🌐 Clicked results/pages are evaluated for educational content');
console.log('❌ Only irrelevant searches and non-educational pages are blocked');

module.exports = { extractSearchQuery, isSearchPlatform, getDomain };
