const { Groq } = require('groq-sdk');
require('dotenv').config();

const GROQ_API_KEY = process.env.GROQ_API_KEY;

if (!GROQ_API_KEY) {
  console.error('❌ GROQ_API_KEY not set in .env file');
  console.log('Please add your Groq API key to the .env file:');
  console.log('GROQ_API_KEY=your_actual_api_key_here');
  process.exit(1);
}

// Initialize Groq client
const groq = new Groq({
  apiKey: GROQ_API_KEY
});

async function testGroqConnection() {
  console.log('🔍 Testing Groq API Connection...\n');

  try {
    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: "You are a helpful AI assistant. Respond with a JSON object containing 'status' and 'message' fields."
        },
        {
          role: "user",
          content: 'Please respond with {"status": "success", "message": "Groq API is working correctly!"}'
        }
      ],
      model: "openai/gpt-oss-20b",
      temperature: 0.1,
      max_completion_tokens: 100,
      top_p: 0.8
    });

    const responseContent = chatCompletion.choices[0]?.message?.content;
    console.log('✅ Raw Response:', responseContent);
    
    if (responseContent) {
      try {
        const parsed = JSON.parse(responseContent);
        console.log('✅ Parsed Response:', parsed);
        console.log(`\n🎉 ${parsed.message || 'Groq API connection successful!'}`);
      } catch (parseError) {
        console.log('⚠️ Response received but not valid JSON:', responseContent);
        console.log('✅ Connection working, but response format needs adjustment');
      }
    }

  } catch (error) {
    console.error('❌ Groq API Error:', error.message);
    console.log('\nPlease check:');
    console.log('1. Your API key is correct in the .env file');
    console.log('2. You have sufficient credits/quota');
    console.log('3. Your network connection is working');
  }
}

// Test a simple query evaluation
async function testQueryEvaluation() {
  console.log('\n🧪 Testing Query Evaluation...\n');

  try {
    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: `Evaluate if a search query is relevant to a learning domain. Respond with JSON: {"isRelevant": true/false, "reasoning": "explanation", "confidence": 0.0-1.0}`
        },
        {
          role: "user",
          content: `Learning Domain: "Machine Learning"
Search Query: "neural networks tutorial"

Is this query relevant?`
        }
      ],
      model: "openai/gpt-oss-20b",
      temperature: 0.1,
      max_completion_tokens: 150,
      top_p: 0.8
    });

    const responseContent = chatCompletion.choices[0]?.message?.content;
    console.log('Query: "neural networks tutorial" for "Machine Learning"');
    console.log('Response:', responseContent);
    
    const evaluation = JSON.parse(responseContent);
    console.log(`Result: ${evaluation.isRelevant ? '✅ RELEVANT' : '❌ NOT RELEVANT'}`);
    console.log(`Reasoning: ${evaluation.reasoning}`);
    console.log(`Confidence: ${(evaluation.confidence * 100).toFixed(1)}%`);

  } catch (error) {
    console.error('❌ Query evaluation error:', error.message);
  }
}

async function runTests() {
  await testGroqConnection();
  await testQueryEvaluation();
  console.log('\n✅ Groq integration testing complete!');
}

if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testGroqConnection, testQueryEvaluation };
