const http = require('http');
const fs = require('fs').promises;

const server = http.createServer(async (req, res) => {
  if (req.url === '/' && req.method === 'GET') {
    const html = await fs.readFile('intent.html', 'utf8');
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(html);
  } else if (req.url === '/submit-intent' && req.method === 'POST') {
    let body = '';
    req.on('data', chunk => body += chunk);
    req.on('end', () => {
      console.log('Received intent:', JSON.parse(body).intent);
      res.writeHead(200);
      res.end();
    });
  }
});

server.listen(3000, () => console.log('Server running on http://localhost:3000'));