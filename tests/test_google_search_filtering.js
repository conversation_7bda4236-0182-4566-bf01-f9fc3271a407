const { Groq } = require('groq-sdk');
require('dotenv').config();

const GROQ_API_KEY = process.env.GROQ_API_KEY;

// Initialize Groq client
const groq = new Groq({
  apiKey: GROQ_API_KEY
});

// Test Google search query filtering specifically
async function testGoogleSearchFiltering() {
  console.log('🔍 Testing Google Search Query Filtering\n');

  const testCases = [
    {
      domain: 'Machine Learning',
      searchUrls: [
        'https://www.google.com/search?q=neural+networks+tutorial',
        'https://www.google.com/search?q=funny+cat+videos',
        'https://www.google.com/search?q=python+scikit+learn',
        'https://www.google.com/search?q=instagram+stories',
        'https://www.google.com/search?q=deep+learning+algorithms',
        'https://www.google.com/',  // Google homepage - should always be allowed
        'https://www.google.com'   // Google homepage - should always be allowed
      ]
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n=== Testing for learning domain: "${testCase.domain}" ===`);
    
    for (const url of testCase.searchUrls) {
      console.log(`\n🌐 URL: ${url}`);
      
      // Extract domain
      const domain = getDomain(url);
      console.log(`   Domain: ${domain}`);
      
      // Check if it's a search platform
      const isSearchPlatform = checkIsSearchPlatform(domain);
      console.log(`   Is Search Platform: ${isSearchPlatform ? '✅ Yes' : '❌ No'}`);
      
      if (isSearchPlatform) {
        // Extract search query
        const searchQuery = extractSearchQuery(url);
        console.log(`   Search Query: ${searchQuery || 'None (homepage/navigation)'}`);
        
        if (searchQuery) {
          console.log(`   🤖 Evaluating query relevance...`);
          const isRelevant = await evaluateSearchQuery(searchQuery, testCase.domain);
          console.log(`   Result: ${isRelevant ? '✅ ALLOWED' : '❌ BLOCKED'}`);
          console.log(`   Action: ${isRelevant ? 'Continue to search results' : 'Redirect back to safe page'}`);
        } else {
          console.log(`   Result: ✅ ALWAYS ALLOWED (Google homepage/navigation)`);
          console.log(`   Action: Allow normal browsing`);
        }
      } else {
        console.log(`   Result: Will be evaluated as regular page content`);
      }
    }
  }
}

// Helper functions (copied from main file)
function getDomain(url) {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname;
  } catch {
    return '';
  }
}

function checkIsSearchPlatform(domain) {
  const searchPlatforms = [
    'google.com', 'youtube.com', 'bing.com', 'yahoo.com', 'duckduckgo.com',
    'search.yahoo.com', 'yandex.com', 'baidu.com'
  ];
  return searchPlatforms.some(platform => 
    domain.includes(platform) || platform.includes(domain)
  );
}

function extractSearchQuery(url) {
  try {
    const urlObj = new URL(url);
    const params = urlObj.searchParams;
    
    // Google search
    if (url.includes('google.com/search')) {
      return params.get('q') || '';
    }
    
    // YouTube search
    if (url.includes('youtube.com/results')) {
      return params.get('search_query') || '';
    }
    
    // Bing search
    if (url.includes('bing.com/search')) {
      return params.get('q') || '';
    }
    
    // Yahoo search
    if (url.includes('search.yahoo.com/search')) {
      return params.get('p') || '';
    }
    
    return '';
  } catch {
    return '';
  }
}

// Evaluate search query relevance using Groq
async function evaluateSearchQuery(query, intent) {
  if (!query.trim()) return true;
  
  try {
    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: `You are an AI assistant that evaluates whether search queries are relevant to a user's learning domain.

TASK: Analyze if the search query is relevant to the user's learning domain and respond with a JSON object.

EVALUATION CRITERIA:
1. Direct Relevance: Is the query directly related to the learning domain?
2. Educational Intent: Does the query suggest educational/learning purposes?
3. Topic Alignment: Does the query align with studying the specified domain?

RELEVANT EXAMPLES:
- Learning Domain: "Machine Learning" → Query: "neural networks tutorial" ✓
- Learning Domain: "Web Development" → Query: "JavaScript functions" ✓
- Learning Domain: "Physics" → Query: "quantum mechanics explained" ✓

IRRELEVANT EXAMPLES:
- Learning Domain: "Machine Learning" → Query: "funny cat videos" ✗
- Learning Domain: "Web Development" → Query: "latest celebrity news" ✗
- Learning Domain: "Physics" → Query: "Instagram stories" ✗

Respond ONLY with a JSON object in this exact format:
{"isRelevant": true/false, "reasoning": "brief explanation", "confidence": 0.0-1.0}`
        },
        {
          role: "user",
          content: `USER'S LEARNING DOMAIN: "${intent}"
SEARCH QUERY: "${query}"`
        }
      ],
      model: "openai/gpt-oss-20b",
      temperature: 0.1,
      max_completion_tokens: 200,
      top_p: 0.8
    });

    const responseContent = chatCompletion.choices[0]?.message?.content;
    if (!responseContent) {
      console.error('No response content from Groq for query evaluation');
      return true;
    }

    // Parse the JSON response
    const evaluation = JSON.parse(responseContent);
    
    console.log(`     - Reasoning: ${evaluation.reasoning}`);
    console.log(`     - Confidence: ${(evaluation.confidence * 100).toFixed(1)}%`);
    
    return evaluation.isRelevant;
  } catch (error) {
    console.error('Groq API error for query evaluation:', error.message);
    return true;
  }
}

// Run the test
if (require.main === module) {
  testGoogleSearchFiltering().then(() => {
    console.log('\n✅ Google search filtering test completed!');
    console.log('\n📋 Summary:');
    console.log('✅ Google homepage/navigation is always allowed');
    console.log('🔍 Search queries are evaluated for relevance to learning domain');
    console.log('✅ Relevant queries are allowed to proceed');
    console.log('❌ Irrelevant queries are blocked and user is redirected');
  }).catch(console.error);
}

module.exports = { testGoogleSearchFiltering };
