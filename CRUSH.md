```crush
# Build/Lint/Test Commands

## Build
npm run start-electron

## Lint
npx eslint . --ext .js

## Test
# All tests
npx playwright test

# Single test file
npx playwright test tests/test_google_search_filtering.js

# Test with debug output
PLAYWRIGHT_DEBUG=1 npx playwright test

# Code Style Guidelines
- Follow Airbnb JavaScript style guide
- Use ESLint with React plugin
- Import order: external -> internal -> relative
- Use camelCase for variables/functions
- PascalCase for class/components
- Add JSDoc for public functions
- Type annotations required for exported modules
- Handle errors with try/catch in async functions
- Use optional chaining for object access
- Prefer default exports for single exports
- Keep functions pure where possible
- Add console.log debugging statements
```