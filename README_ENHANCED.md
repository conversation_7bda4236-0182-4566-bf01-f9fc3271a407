# Enhanced Study Browser - Search Query & Result Filtering

## Overview
The Study Browser has been enhanced to implement intelligent search query and result filtering instead of blocking Google and YouTube entirely. This provides a more refined approach to keeping users focused on their learning domain.

## New Features

### 🔍 Search Query Filtering
- **Google/YouTube searches are analyzed** for relevance to your learning domain
- **Irrelevant queries are blocked** immediately (e.g., searching "Instagram" when learning "Machine Learning")
- **Relevant queries are allowed** (e.g., searching "neural networks tutorial" when learning "Machine Learning")
- **Search platforms themselves are never blocked** - only inappropriate search queries

### 🌐 Result/Page Filtering
- **Clicked links are evaluated** for educational relevance
- **Educational content is allowed** regardless of the platform
- **Non-educational content is blocked** to prevent distractions
- **Intelligent AI evaluation** determines if page content aligns with your learning domain

### ✅ Protected Search Platforms
The following search platforms are **never blocked**:
- Google (google.com)
- YouTube (youtube.com) 
- Bing (bing.com)
- Yahoo Search (search.yahoo.com)
- DuckDuckGo (duckduckgo.com)

## How It Works

### 1. Learning Domain Setup
When you start the browser, you'll be prompted to enter your learning domain (e.g., "Machine Learning", "Web Development", "Physics").

### 2. Search Query Analysis
When you search on Google/YouTube/etc.:
- ✅ **Relevant query example**: Learning "Machine Learning" → Search "Python neural networks" → **ALLOWED**
- ❌ **Irrelevant query example**: Learning "Machine Learning" → Search "funny cat videos" → **BLOCKED**

### 3. Result Page Analysis  
When you click on search results:
- ✅ **Educational content**: Wikipedia articles, tutorials, documentation → **ALLOWED**
- ❌ **Non-educational content**: Social media, entertainment, shopping → **BLOCKED**

## Technical Implementation

### Key Functions Added:

1. **`isSearchPlatform(domain)`**: Identifies search platforms that should never be blocked
2. **`extractSearchQuery(url)`**: Extracts search queries from Google, YouTube, and other search URLs
3. **`evaluateSearchQuery(query, intent)`**: Uses AI to evaluate if a search query is relevant to the learning domain
4. **Enhanced `evaluateWithGemini()`**: Improved AI evaluation for page content relevance

### Search Query Detection:
- **Google**: `google.com/search?q=QUERY`
- **YouTube**: `youtube.com/results?search_query=QUERY`
- **Bing**: `bing.com/search?q=QUERY`
- **Yahoo**: `search.yahoo.com/search?p=QUERY`

## Example Scenarios

### Scenario 1: Machine Learning Student
**Learning Domain**: "Machine Learning"

**Search Queries**:
- ✅ "Python scikit-learn tutorial" → ALLOWED
- ✅ "neural network architecture" → ALLOWED  
- ✅ "TensorFlow documentation" → ALLOWED
- ❌ "funny memes" → BLOCKED
- ❌ "celebrity news" → BLOCKED

**Clicked Results**:
- ✅ Khan Academy ML course → ALLOWED
- ✅ Stack Overflow Python question → ALLOWED
- ❌ Instagram feed → BLOCKED
- ❌ Gaming website → BLOCKED

### Scenario 2: Web Development Student  
**Learning Domain**: "Web Development"

**Search Queries**:
- ✅ "React hooks tutorial" → ALLOWED
- ✅ "CSS flexbox guide" → ALLOWED
- ✅ "JavaScript async await" → ALLOWED
- ❌ "TikTok videos" → BLOCKED
- ❌ "sports news" → BLOCKED

## Usage Instructions

### 1. Setup Environment
Ensure you have a `.env` file with your Gemini API key:
```
GEMINI_API_KEY=your_api_key_here
```

### 2. Run the Study Browser
```bash
npm start
```

### 3. Enter Learning Domain
When prompted, enter your specific learning domain (e.g., "Machine Learning", "Web Development").

### 4. Browse Normally
- Use Google, YouTube, or other search engines normally
- Irrelevant searches will be blocked with explanations
- Educational content will be allowed regardless of platform

## Testing

Run the test script to see how search query filtering works:
```bash
node test_search_filtering.js
```

This will test various search queries against different learning domains and show which ones would be allowed or blocked.

## Benefits

1. **🎯 Focused Learning**: Blocks distracting searches while allowing educational ones
2. **🔓 Platform Access**: Never blocks useful platforms like Google and YouTube
3. **🤖 Intelligent Filtering**: AI-powered evaluation ensures accurate relevance detection
4. **📊 Detailed Logging**: All search queries and decisions are logged for review
5. **⚡ Real-time Feedback**: Immediate notifications about allowed/blocked content

## Configuration

The system automatically maintains safe and blocked site lists in `sites.json`. Search platforms are automatically added to the safe list and removed from any blacklists to ensure they're never blocked.

## Future Enhancements

- Support for additional search engines
- Custom learning domain templates
- Study session analytics
- Allowlist for specific educational channels/creators
- Time-based filtering (e.g., allow entertainment during breaks)
