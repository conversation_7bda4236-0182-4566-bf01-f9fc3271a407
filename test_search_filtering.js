const { Groq } = require('groq-sdk');
require('dotenv').config();

const GROQ_API_KEY = process.env.GROQ_API_KEY;

// Initialize Groq client
const groq = new Groq({
  apiKey: GROQ_API_KEY
});

// Test the search query filtering functionality
async function testSearchQueryFiltering() {
  console.log('Testing Search Query Filtering...\n');

  const testCases = [
    {
      intent: 'Machine Learning',
      queries: [
        'neural networks tutorial',
        'funny cat videos',
        'Python scikit-learn',
        'Instagram stories',
        'deep learning algorithms',
        'celebrity gossip'
      ]
    },
    {
      intent: 'Web Development',
      queries: [
        'JavaScript functions',
        'React tutorial',
        'TikTok dances',
        'HTML CSS guide',
        'gaming livestream',
        'Node.js express'
      ]
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n=== Testing for domain: "${testCase.intent}" ===`);
    
    for (const query of testCase.queries) {
      console.log(`\nQuery: "${query}"`);
      const isRelevant = await evaluateSearchQuery(query, testCase.intent);
      console.log(`Result: ${isRelevant ? '✅ ALLOWED' : '❌ BLOCKED'}`);
    }
  }
}

// Evaluate search query relevance using Groq
async function evaluateSearchQuery(query, intent) {
  if (!query.trim()) return true;
  
  try {
    const chatCompletion = await groq.chat.completions.create({
      messages: [
        {
          role: "system",
          content: `You are an AI assistant that evaluates whether search queries are relevant to a user's learning domain.

TASK: Analyze if the search query is relevant to the user's learning domain and respond with a JSON object.

EVALUATION CRITERIA:
1. Direct Relevance: Is the query directly related to the learning domain?
2. Educational Intent: Does the query suggest educational/learning purposes?
3. Topic Alignment: Does the query align with studying the specified domain?

RELEVANT EXAMPLES:
- Learning Domain: "Machine Learning" → Query: "neural networks tutorial" ✓
- Learning Domain: "Web Development" → Query: "JavaScript functions" ✓
- Learning Domain: "Physics" → Query: "quantum mechanics explained" ✓

IRRELEVANT EXAMPLES:
- Learning Domain: "Machine Learning" → Query: "funny cat videos" ✗
- Learning Domain: "Web Development" → Query: "latest celebrity news" ✗
- Learning Domain: "Physics" → Query: "Instagram stories" ✗

Respond ONLY with a JSON object in this exact format:
{"isRelevant": true/false, "reasoning": "brief explanation", "confidence": 0.0-1.0}`
        },
        {
          role: "user",
          content: `USER'S LEARNING DOMAIN: "${intent}"
SEARCH QUERY: "${query}"`
        }
      ],
      model: "openai/gpt-oss-20b",
      temperature: 0.1,
      max_completion_tokens: 200,
      top_p: 0.8
    });

    const responseContent = chatCompletion.choices[0]?.message?.content;
    if (!responseContent) {
      console.error('No response content from Groq for query evaluation');
      return true;
    }

    // Parse the JSON response
    const evaluation = JSON.parse(responseContent);
    
    console.log(`  - Reasoning: ${evaluation.reasoning}`);
    console.log(`  - Confidence: ${(evaluation.confidence * 100).toFixed(1)}%`);
    
    return evaluation.isRelevant;
  } catch (error) {
    console.error('Groq API error for query evaluation:', error.message);
    return true;
  }
}

// Run the test
if (require.main === module) {
  testSearchQueryFiltering().catch(console.error);
}

module.exports = { testSearchQueryFiltering, evaluateSearchQuery };
