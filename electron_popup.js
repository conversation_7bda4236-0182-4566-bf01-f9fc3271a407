const { app, BrowserWindow } = require('electron');
const path = require('path');
const net = require('net');

let popupWindow = null;

function createPopupWindow() {
  popupWindow = new BrowserWindow({
    width: 300,
    height: 100,
    x: 20, // Top-right corner (adjust for your screen)
    y: 20,
    frame: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    transparent: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  });

  popupWindow.loadFile('popup.html');
  popupWindow.setVisibleOnAllWorkspaces(true);
  popupWindow.hide(); // Hidden until a message is received

  // Debug: Log window events
  popupWindow.on('closed', () => {
    console.log('Popup window closed');
    popupWindow = null;
  });
}

app.whenReady().then(() => {
  createPopupWindow();

  // Start TCP server to receive messages from Playwright
  const server = net.createServer((socket) => {
    console.log('Playwright connected to TCP server');
    socket.on('data', (data) => {
      try {
        const { message, type } = JSON.parse(data.toString());
        console.log('Received popup request:', message, type);
        if (popupWindow && !popupWindow.isDestroyed()) {
          popupWindow.webContents.send('show-popup', { message, type });
          popupWindow.showInactive(); // Show without stealing focus
          setTimeout(() => {
            if (popupWindow && !popupWindow.isDestroyed()) {
              popupWindow.hide();
            }
          }, 3000);
        }
      } catch (error) {
        console.error('Error processing popup request:', error);
      }
    });

    socket.on('end', () => {
      console.log('Playwright disconnected from TCP server');
    });
  });

  server.listen(3001, 'localhost', () => {
    console.log('TCP server listening on localhost:3001');
  });

  server.on('error', (error) => {
    console.error('TCP server error:', error);
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createPopupWindow();
  }
});